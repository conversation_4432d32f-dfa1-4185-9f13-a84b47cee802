import { v } from "convex/values";

import { mutation, query } from "./_generated/server";

export const CreateNewDesign = mutation({
  args: {
    name: v.string(),
    width: v.number(),
    height: v.number(),
    uid: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Validasi user exists
    const user = await ctx.db.get(args.uid);
    if (!user) {
      throw new Error(`User dengan ID ${args.uid} tidak ditemukan`);
    }

    // Cek limit untuk free users
    const userSubscriptionType = user.subscriptionType || "free";
    if (userSubscriptionType === "free") {
      const userDesigns = await ctx.db
        .query("designs")
        .filter((q) => q.eq(q.field("uid"), args.uid))
        .collect();

      if (userDesigns.length >= 5) {
        throw new Error(
          "Limit desain gratis tercapai. Upgrade ke premium untuk membuat lebih banyak desain."
        );
      }
    }

    const result = await ctx.db.insert("designs", {
      name: args.name,
      height: args.height,
      width: args.width,
      uid: args.uid,
      createdAt: Date.now(),
    });

    return result;
  },
});

export const GetDesign = query({
  args: {
    id: v.id("designs"),
  },
  handler: async (ctx, args) => {
    const result = await ctx.db.get(args.id);
    return result;
  },
});

export const SaveDesign = mutation({
  args: {
    id: v.id("designs"),
    jsonDesign: v.any(),
    imagePreview: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const result = await ctx.db.patch(args.id, {
      jsonTemplate: args.jsonDesign,
      imagePreview: args?.imagePreview,
    });

    return result;
  },
});

export const GetUserDesigns = query({
  args: {
    uid: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Validasi user exists
    const user = await ctx.db.get(args.uid);
    if (!user) {
      throw new Error(`User dengan ID ${args.uid} tidak ditemukan`);
    }

    const result = await ctx.db
      .query("designs")
      .filter((q) => q.eq(q.field("uid"), args.uid))
      .order("desc", "createdAt")
      .collect();

    // Add timestamp to ensure fresh data
    const resultWithTimestamp = result.map((design) => ({
      ...design,
      _fetchedAt: Date.now(),
    }));

    return resultWithTimestamp;
  },
});

export const CreateDesignFromTemplate = mutation({
  args: {
    name: v.string(),
    imagePreview: v.string(),
    jsonTemplate: v.any(),
    uid: v.id("users"),
    width: v.number(),
    height: v.number(),
  },
  handler: async (ctx, args) => {
    // Validasi user exists
    const user = await ctx.db.get(args.uid);
    if (!user) {
      throw new Error(`User dengan ID ${args.uid} tidak ditemukan`);
    }

    // Cek limit untuk free users
    const userSubscriptionType = user.subscriptionType || "free";
    if (userSubscriptionType === "free") {
      const userDesigns = await ctx.db
        .query("designs")
        .filter((q) => q.eq(q.field("uid"), args.uid))
        .collect();

      if (userDesigns.length >= 5) {
        throw new Error(
          "Limit desain gratis tercapai. Upgrade ke premium untuk membuat lebih banyak desain."
        );
      }
    }

    const result = await ctx.db.insert("designs", {
      name: args.name,
      uid: args.uid,
      height: args.height,
      width: args.width,
      imagePreview: args?.imagePreview,
      jsonTemplate: args?.jsonTemplate,
      createdAt: Date.now(),
    });

    return result;
  },
});

export const GetUserDesignCount = query({
  args: {
    uid: v.id("users"),
  },
  handler: async (ctx, args) => {
    const designs = await ctx.db
      .query("designs")
      .filter((q) => q.eq(q.field("uid"), args.uid))
      .collect();

    return designs.length;
  },
});

export const RenameDesign = mutation({
  args: {
    id: v.id("designs"),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    // Validasi design exists
    const design = await ctx.db.get(args.id);
    if (!design) {
      throw new Error(`Desain dengan ID ${args.id} tidak ditemukan`);
    }

    const result = await ctx.db.patch(args.id, {
      name: args.name,
    });

    return result;
  },
});

export const DeleteDesign = mutation({
  args: {
    id: v.id("designs"),
  },
  handler: async (ctx, args) => {
    // Validasi design exists
    const design = await ctx.db.get(args.id);
    if (!design) {
      throw new Error(`Desain dengan ID ${args.id} tidak ditemukan`);
    }

    const result = await ctx.db.delete(args.id);
    return result;
  },
});
