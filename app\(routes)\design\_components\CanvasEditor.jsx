import { Canvas } from "fabric";
import React, { useEffect, useRef, useState } from "react";

import { useCanvasHook } from "@/hooks/useCanvas";
import { useCanvasOperations } from "@/hooks/useCanvasOperations";
import TopNavBar from "@/services/Components/TopNavBar";
import LayerPanel from "@/services/Components/LayerPanel";
import FloatingToggle from "@/services/Components/FloatingToggle";

function CanvasEditor({ DesignInfo }) {
  const canvasRef = useRef();
  const { canvasEditor, setCanvasEditor } = useCanvasHook();
  const { moveObjectWithKeys, deleteObject } =
    useCanvasOperations(canvasEditor);

  // State untuk toggle panels
  const [showFloatingSidebar, setShowFloatingSidebar] = useState(false);
  const [showFloatingLayer, setShowFloatingLayer] = useState(false);
  const [objectCount, setObjectCount] = useState(0);
  const [layerPanelMinimized, setLayerPanelMinimized] = useState(false);

  /**
   * Gunakan untuk menginisialisasi canvas
   */
  useEffect(() => {
    if (canvasRef.current && DesignInfo) {
      const initCanvas = new Canvas(canvasRef.current, {
        width: DesignInfo?.width,
        height: DesignInfo?.height,
        backgroundColor: "#fff",
        preserveObjectStacking: true,
        controlsAboveOverlay: true,
        // Enable multi-selection
        selection: true,
        selectionBorderColor: "#4f46e5",
        selectionLineWidth: 2,
        selectionDashArray: [5, 5],
        // Enable object caching for better performance
        enableRetinaScaling: true,
        // Allow objects to be selected with click
        interactive: true,
      });

      if (DesignInfo?.jsonTemplate) {
        initCanvas.loadFromJSON(DesignInfo?.jsonTemplate, () => {
          initCanvas?.requestRenderAll();
        });
      }

      initCanvas.renderAll();

      setCanvasEditor(initCanvas);

      return () => {
        initCanvas.dispose();
      };
    }
  }, [DesignInfo]);

  /**
   * Gunakan untuk keyboard shortcuts dan object operations
   */
  useEffect(() => {
    const handleKeyDown = (event) => {
      console.log(
        "Key pressed:",
        event.key,
        "Ctrl:",
        event.ctrlKey,
        "Shift:",
        event.shiftKey
      );

      if (!canvasEditor) {
        console.log("No canvas editor available");
        return;
      }

      const activeObject = canvasEditor.getActiveObject();
      const isEditing = activeObject?.isEditing;

      console.log("Active object:", activeObject, "Is editing:", isEditing);

      // Jangan proses shortcuts jika sedang editing text
      if (isEditing) {
        console.log("Text editing mode, skipping shortcuts");
        return;
      }

      // Delete object
      if (event.key === "Delete" && activeObject) {
        console.log("Deleting object");
        deleteObject();
        return;
      }

      // Move objects with arrow keys
      if (
        ["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(
          event.key
        ) &&
        activeObject
      ) {
        console.log("Moving object with arrow key");
        event.preventDefault();
        moveObjectWithKeys(event.key, event.shiftKey ? 10 : 1);
        return;
      }

      // Layer management shortcuts
      if (activeObject && event.ctrlKey) {
        switch (event.key) {
          case "l":
          case "L":
            // Ctrl+L: Toggle lock
            event.preventDefault();
            const isLocked = activeObject.selectable === false;
            activeObject.set({
              selectable: isLocked,
              evented: isLocked,
            });
            if (!isLocked) {
              canvasEditor.discardActiveObject();
            }
            canvasEditor.requestRenderAll();
            console.log(`Object ${isLocked ? "unlocked" : "locked"}`);
            return;

          case "h":
          case "H":
            // Ctrl+H: Toggle visibility
            event.preventDefault();
            const isVisible = activeObject.visible !== false;
            activeObject.set({ visible: !isVisible });
            if (!isVisible) {
              canvasEditor.discardActiveObject();
            }
            canvasEditor.requestRenderAll();
            console.log(`Object ${isVisible ? "hidden" : "shown"}`);
            return;

          case "]":
            // Ctrl+]: Bring forward
            event.preventDefault();
            canvasEditor.bringForward(activeObject);
            canvasEditor.requestRenderAll();
            console.log("Object brought forward");
            return;

          case "[":
            // Ctrl+[: Send backward
            event.preventDefault();
            canvasEditor.sendBackwards(activeObject);
            canvasEditor.requestRenderAll();
            console.log("Object sent backward");
            return;
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [canvasEditor, moveObjectWithKeys, deleteObject]);

  /**
   * Track object count untuk floating toggle
   */
  useEffect(() => {
    if (!canvasEditor) return;

    const updateObjectCount = () => {
      const objects = canvasEditor.getObjects();
      setObjectCount(objects.length);
    };

    // Initial count
    updateObjectCount();

    // Listen to canvas events
    canvasEditor.on("object:added", updateObjectCount);
    canvasEditor.on("object:removed", updateObjectCount);

    return () => {
      canvasEditor.off("object:added", updateObjectCount);
      canvasEditor.off("object:removed", updateObjectCount);
    };
  }, [canvasEditor]);

  // Handle layer panel minimize state
  useEffect(() => {
    console.log(
      "CanvasEditor layerPanelMinimized changed to:",
      layerPanelMinimized
    );
    setShowFloatingLayer(layerPanelMinimized);
  }, [layerPanelMinimized]);

  return (
    <div className="bg-secondary w-full min-h-screen flex flex-col relative">
      <TopNavBar />
      <div className="flex flex-1 mt-10">
        {/* Canvas Area */}
        <div className="flex-1 flex items-start justify-center relative overflow-auto p-4">
          <div className="flex items-center justify-center min-h-full">
            <canvas id="canvas" ref={canvasRef} />
          </div>
        </div>

        {/* Layer Panel */}
        <LayerPanel
          isMinimized={layerPanelMinimized}
          onMinimize={setLayerPanelMinimized}
        />
      </div>

      {/* Floating Toggle Buttons */}
      <FloatingToggle
        showSidebarToggle={showFloatingSidebar}
        showLayerToggle={showFloatingLayer}
        onSidebarToggle={() => setShowFloatingSidebar(false)}
        onLayerToggle={() => setLayerPanelMinimized(false)}
        objectCount={objectCount}
      />
    </div>
  );
}

export default CanvasEditor;
