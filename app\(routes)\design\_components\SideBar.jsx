import { useState } from "react";
import { ChevronLeft, ChevronRight, X } from "lucide-react";

import { sideBarMenu } from "@/services/Options";
import { Button } from "@/components/ui/button";
import { useMobile } from "@/hooks/useMobile";

import SideBarSettings from "./SideBarSettings";

function SideBar({ isOpen = false, onClose }) {
  const [selectedOption, setSelectedOption] = useState();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const isMobile = useMobile();

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
    if (!isCollapsed) {
      setSelectedOption(null); // Close settings when collapsing
    }
  };

  const handleMobileClose = () => {
    onClose?.();
    setSelectedOption(null);
  };

  // Mobile overlay
  if (isMobile) {
    return (
      <>
        {/* Mobile Overlay */}
        {isOpen && (
          <div className="mobile-sidebar-overlay" onClick={handleMobileClose} />
        )}

        {/* Mobile Sidebar */}
        <div className={`mobile-sidebar ${isOpen ? "open" : ""}`}>
          {/* Mobile Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="font-semibold text-gray-900">Tools</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMobileClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Mobile Menu Items */}
          <div className="p-4 space-y-3">
            {sideBarMenu.map((menu, index) => (
              <div
                key={index}
                className={`p-3 flex items-center gap-3 hover:bg-secondary cursor-pointer rounded-lg transition-all
                  ${menu.name == selectedOption?.name && "bg-secondary"}`}
                onClick={() =>
                  setSelectedOption(
                    selectedOption?.name === menu.name ? null : menu
                  )
                }
              >
                <menu.icon strokeWidth={1.5} className="h-5 w-5" />
                <span className="text-sm">{menu.name}</span>
              </div>
            ))}
          </div>

          {/* Mobile Settings Panel */}
          {selectedOption && (
            <div className="border-t bg-gray-50 p-4">
              <SideBarSettings selectedOption={selectedOption} />
            </div>
          )}
        </div>
      </>
    );
  }

  return (
    <div className="flex relative">
      {/* Main Sidebar */}
      <div
        className={`transition-all duration-300 ease-in-out border-r min-h-full pt-2 bg-white
          ${isCollapsed ? "w-[60px]" : "w-[120px]"}`}
      >
        {/* Toggle Button */}
        <div className="flex justify-end p-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="h-6 w-6 p-0 hover:bg-gray-100"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Menu Items */}
        <div className="px-2">
          {sideBarMenu.map((menu, index) => (
            <div
              key={index}
              className={`p-2 mb-3 flex flex-col items-center hover:bg-secondary cursor-pointer rounded-lg transition-all
                ${menu.name == selectedOption?.name && "bg-secondary"}
                ${isCollapsed ? "px-1" : "px-2"}`}
              onClick={() => {
                if (isCollapsed) {
                  setIsCollapsed(false);
                  setSelectedOption(menu);
                } else {
                  setSelectedOption(
                    selectedOption?.name === menu.name ? null : menu
                  );
                }
              }}
            >
              <menu.icon
                strokeWidth={1.5}
                className={isCollapsed ? "h-5 w-5" : "h-6 w-6"}
              />
              {!isCollapsed && (
                <h2 className="mt-1 text-xs text-center">{menu.name}</h2>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Settings Panel */}
      {!isCollapsed && (
        <SideBarSettings
          selectedOption={selectedOption}
          onClose={() => setSelectedOption(null)}
        />
      )}
    </div>
  );
}

export default SideBar;
