"use client";

import { useUser } from "@stackframe/stack";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function Home() {
  const user = useUser();
  const router = useRouter();

  const handleGetStarted = () => {
    if (user) {
      router.push("/workspace");
    } else {
      router.push("/handler/sign-in?after_auth_return_to=%2Fworkspace");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="container mx-auto px-4 py-6 flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Image src="/logo.png" alt="Canvain" width={120} height={40} />
        </div>
        <div className="flex items-center gap-4">
          {user ? (
            <Button
              onClick={() => router.push("/workspace")}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Buka Workspace
            </Button>
          ) : (
            <>
              <Link href="/handler/sign-in">
                <Button
                  variant="ghost"
                  className="text-gray-700 hover:text-purple-600"
                >
                  Masuk
                </Button>
              </Link>
              <Link href="/handler/sign-up">
                <Button className="bg-purple-600 hover:bg-purple-700">
                  Daftar Gratis
                </Button>
              </Link>
            </>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Desain Apapun dengan
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">
              {" "}
              CanvaIn
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            Platform desain grafis yang mudah digunakan untuk membuat poster,
            banner, presentasi, dan konten visual lainnya. Tanpa perlu keahlian
            desain!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              onClick={handleGetStarted}
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 text-lg"
            >
              Mulai Desain Gratis
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() =>
                document
                  .getElementById("features")
                  .scrollIntoView({ behavior: "smooth" })
              }
              className="border-purple-200 text-purple-600 hover:bg-purple-50 px-8 py-4 text-lg"
            >
              Lihat Fitur
            </Button>
          </div>
        </div>
      </section>

      {/* Preview Image */}
      <section className="container mx-auto px-4 pb-20">
        <div className="max-w-5xl mx-auto">
          <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-4">
            <Image
              src="/banner-home.png"
              alt="Canvain Preview"
              width={1200}
              height={600}
              className="w-full h-auto rounded-xl"
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Fitur Unggulan CanvaIn
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Semua yang Anda butuhkan untuk membuat desain profesional
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
              <svg
                className="w-6 h-6 text-purple-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Template Siap Pakai
            </h3>
            <p className="text-gray-600">
              Ribuan template profesional untuk berbagai kebutuhan desain Anda
            </p>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
              <svg
                className="w-6 h-6 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Editor Drag & Drop
            </h3>
            <p className="text-gray-600">
              Interface yang intuitif, cukup seret dan lepas untuk mendesain
            </p>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
              <svg
                className="w-6 h-6 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Export Berkualitas
            </h3>
            <p className="text-gray-600">
              Download desain dalam format PNG, JPG, atau PDF dengan kualitas
              tinggi
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-12 text-center text-white">
          <h2 className="text-4xl font-bold mb-4">
            Siap Membuat Desain Menakjubkan?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Bergabung dengan ribuan pengguna yang sudah mempercayai CanvaIn
          </p>
          <Button
            onClick={handleGetStarted}
            size="lg"
            className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
          >
            Mulai Sekarang - Gratis!
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-12 border-t border-gray-200">
        <div className="text-center text-gray-600">
          <p>&copy; 2025 CanvaIn. Semua hak dilindungi.</p>
        </div>
      </footer>
    </div>
  );
}
