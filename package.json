{"name": "canvain", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@stackframe/stack": "^2.8.12", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.1", "fabric": "^6.6.5", "html2canvas": "^1.4.1", "imagekit": "^6.0.0", "jspdf": "^2.5.2", "lucide-react": "^0.511.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "^18.0.0", "react-color": "^2.19.3", "react-dom": "^18.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4", "tw-animate-css": "^1.3.0"}}