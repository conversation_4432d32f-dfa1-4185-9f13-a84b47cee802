import { toast } from "sonner";
import { useCallback } from "react";

export const useCanvasOperations = (canvasEditor) => {
  /**
   * Move object dengan arrow keys
   */
  const moveObjectWithKeys = useCallback(
    (key, step = 1) => {
      if (!canvasEditor) {
        console.log("No canvas editor available");
        return;
      }

      const activeObject = canvasEditor.getActiveObject();
      if (!activeObject) {
        console.log("No active object to move");
        return;
      }

      console.log(`Moving object with key: ${key}, step: ${step}`);

      const currentLeft = activeObject.left;
      const currentTop = activeObject.top;

      switch (key) {
        case "ArrowUp":
          activeObject.set({ top: currentTop - step });
          break;
        case "ArrowDown":
          activeObject.set({ top: currentTop + step });
          break;
        case "ArrowLeft":
          activeObject.set({ left: currentLeft - step });
          break;
        case "ArrowRight":
          activeObject.set({ left: currentLeft + step });
          break;
      }

      activeObject.setCoords();
      canvasEditor.requestRenderAll();
      console.log(
        `Object moved from (${currentLeft}, ${currentTop}) to (${activeObject.left}, ${activeObject.top})`
      );
    },
    [canvasEditor]
  );

  /**
   * Delete selected object
   */
  const deleteObject = useCallback(() => {
    if (!canvasEditor) return;

    const activeObject = canvasEditor.getActiveObject();
    if (activeObject && !activeObject.isEditing) {
      canvasEditor.remove(activeObject);
      canvasEditor.requestRenderAll();
      toast.success("Object berhasil dihapus", { duration: 1500 });
    }
  }, [canvasEditor]);

  return {
    moveObjectWithKeys,
    deleteObject,
  };
};
